# 页面刷新报错修复 - 计划

## 实施方案：修复Vis.js配置错误

基于问题分析，将修复JavaScript运行时错误并优化样式显示效果。

## 2.1 修复chosen.node回调错误

### 文件路径
- `src/core/VisJsRenderer.ts`

### 类/方法名
- 修改方法：`getNetworkOptions()`
- 修改区域：chosen.node回调函数

### 修改范围
- 第496-504行：修复shadow配置错误
- 第558-566行：修复回退配置中的相同问题

### 预期结果
- 消除"Cannot create property 'size' on boolean 'true'"错误
- 节点选择和悬停效果正常工作
- 页面刷新不再出现JavaScript错误

## 2.2 优化groups配置格式

### 文件路径
- `src/core/VisJsRenderer.ts`

### 类/方法名
- 修改方法：`getDarkThemeColors()`
- 修改方法：`getLightThemeColors()`
- 修改方法：`getFallbackColors()`

### 修改范围
- 检查所有颜色配置对象的格式
- 确保color、font、shape等属性格式正确
- 验证CSS变量的使用

### 预期结果
- groups配置被Vis.js正确解析
- 节点颜色按主题正确显示
- CSS变量正确解析为实际颜色值

## 2.3 添加配置验证机制

### 文件路径
- `src/core/VisJsRenderer.ts`

### 类/方法名
- 新增方法：`validateNodeConfig()`
- 新增方法：`validateGroupsConfig()`
- 修改方法：`getNetworkOptions()`

### 修改范围
- 新增配置验证逻辑（约30行代码）
- 在getNetworkOptions中调用验证
- 添加详细的错误日志

### 预期结果
- 配置错误能够被提前发现
- 提供清晰的错误信息便于调试
- 自动回退到安全配置

## 2.4 增强错误处理机制

### 文件路径
- `src/core/VisJsRenderer.ts`

### 类/方法名
- 修改方法：`initializeNetwork()`
- 修改方法：`onThemeChange()`
- 新增方法：`handleNetworkError()`

### 修改范围
- 在网络初始化中添加try-catch
- 在主题变化处理中增强错误处理
- 新增网络错误处理方法（约20行代码）

### 预期结果
- 网络初始化失败时有优雅降级
- 主题切换错误不影响基本功能
- 所有错误都有详细日志记录

## 2.5 CSS变量解析优化

### 文件路径
- `styles.css`
- `src/core/VisJsRenderer.ts`

### 类/方法名
- 新增方法：`resolveCSSVariable()`
- 修改CSS变量定义

### 修改范围
- 添加CSS变量解析工具函数（约25行代码）
- 优化CSS变量定义，确保兼容性
- 在颜色配置中使用解析后的值

### 预期结果
- CSS变量能够正确解析为实际颜色值
- 在不同主题下颜色显示正确
- 兼容更多浏览器环境

## 2.6 节点样式应用验证

### 文件路径
- `src/core/VisJsRenderer.ts`

### 类/方法名
- 修改方法：`convertToVisFormat()`
- 新增方法：`applyNodeStyles()`

### 修改范围
- 在节点创建时确保样式正确应用
- 添加样式应用验证逻辑（约15行代码）
- 确保group属性正确设置

### 预期结果
- 每个节点都能正确应用对应的group样式
- 不同类型节点有明显的视觉区分
- 主题切换时样式立即更新

## 2.7 调试和日志增强

### 文件路径
- `src/core/VisJsRenderer.ts`

### 类/方法名
- 修改所有相关方法：添加调试日志
- 新增方法：`logConfigurationState()`

### 修改范围
- 在关键步骤添加调试日志
- 新增配置状态日志方法（约10行代码）
- 优化错误信息的可读性

### 预期结果
- 便于调试和问题定位
- 清晰的配置状态信息
- 详细的错误追踪信息

## 实施顺序

1. **步骤2.1**：优先修复chosen.node回调错误（紧急）
2. **步骤2.2**：优化groups配置格式
3. **步骤2.3** → **步骤2.4**：添加验证和错误处理
4. **步骤2.5** → **步骤2.6**：优化CSS和样式应用
5. **步骤2.7**：完善调试和日志

## 验收标准

每个步骤完成后需要验证：
- 代码编译无错误
- 页面刷新无JavaScript错误
- 节点交互效果正常
- 主题切换时颜色正确更新
- 文字清晰可读，有明显的视觉改善

## 风险控制

- 每次修改后立即测试，确保不引入新问题
- 保留当前工作版本作为备份
- 如遇到复杂问题，可以快速回退到方案三（安全配置）

## 预期修复效果

修复完成后：
1. **无运行时错误**：页面刷新和操作都不会出现JavaScript错误
2. **明显的视觉改善**：不同主题下文字清晰可读，颜色对比度良好
3. **流畅的交互体验**：节点选择、悬停、主题切换都工作正常
4. **稳定的功能表现**：在各种使用场景下都能稳定工作
