/**
 * 核心类型定义
 */

// 插件设置接口
export interface MindMapSyncSettings {
  // AI服务配置
  aiProvider: 'openai' | 'ollama';
  openaiApiKey: string;
  openaiModel: string;
  ollamaUrl: string;
  ollamaModel: string;
  
  // 同步设置
  enableSync: boolean;
  debounceTime: number;
  conflictResolution: 'markdown' | 'mindmap' | 'prompt';
  autoSyncScope: 'current' | 'all';
  
  // 思维导图样式
  layoutDirection: 'horizontal' | 'vertical';
  nodeStylePreset: string;
  enableAiNodeStyle: boolean;
  defaultZoomLevel: number;

  // 连接功能设置
  enableLogicalConnections: boolean;
  defaultConnectionStyle: ConnectionStyle;
  defaultConnectionColor: string;
  defaultConnectionWidth: number;
  showConnectionLabels: boolean;
  maxConnectionsPerNode: number;

  // 高级设置
  debugMode: boolean;
}

// 默认设置
export const DEFAULT_SETTINGS: MindMapSyncSettings = {
  aiProvider: 'ollama',
  openaiApiKey: '',
  openaiModel: 'gpt-4',
  ollamaUrl: 'http://localhost:11434',
  ollamaModel: 'llama2',
  
  enableSync: true,
  debounceTime: 300,
  conflictResolution: 'prompt',
  autoSyncScope: 'current',
  
  layoutDirection: 'horizontal',
  nodeStylePreset: 'default',
  enableAiNodeStyle: true,
  defaultZoomLevel: 1.0,

  enableLogicalConnections: true,
  defaultConnectionStyle: 'solid',
  defaultConnectionColor: '#666666',
  defaultConnectionWidth: 2,
  showConnectionLabels: true,
  maxConnectionsPerNode: 10,

  debugMode: false,
};

// 节点数据接口
export interface NodeData {
  id: string;
  content: string;
  level: number;
  type: 'heading' | 'list' | 'code' | 'ai-generated';
  children: NodeData[];
  position?: {
    line: number;
    ch: number;
  };
  metadata?: {
    isAiGenerated?: boolean;
    aiGeneratedAt?: Date;
    originalQuestion?: string;
  };
}

// 文档位置接口
export interface Position {
  line: number;
  ch: number;
}

// 同步事件接口
export interface SyncEvent {
  type: 'markdown-change' | 'mindmap-change';
  source: 'user' | 'system';
  changes: any[];
  timestamp: Date;
}

// AI请求接口
export interface AIRequest {
  question: string;
  context: string;
  selectedText: string;
  position: Position;
}

// AI响应接口
export interface AIResponse {
  answer: string;
  duration: number;
  model: string;
  timestamp: Date;
}

// 错误类型
export interface PluginError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// 事件类型
export type EventType =
  | 'markdown:change'
  | 'mindmap:node:change'
  | 'mindmap:connection:create'
  | 'mindmap:connection:update'
  | 'mindmap:connection:delete'
  | 'mindmap:connection:select'
  | 'sync:start'
  | 'sync:complete'
  | 'sync:error'
  | 'ai:request:start'
  | 'ai:request:complete'
  | 'ai:request:error';

// 事件数据接口
export interface EventData {
  [key: string]: any;
}

// 思维导图API接口
export interface MindMapAPI {
  render(markdown: string): void;
  updateNode(nodeId: string, data: Partial<NodeData>): void;
  addNode(parentId: string, data: NodeData): string;
  deleteNode(nodeId: string): void;
  moveNode(nodeId: string, targetParentId: string): void;
  getNodeByPosition(pos: Position): NodeData | null;
  getPositionByNode(nodeId: string): Position | null;
  setNodeChangeCallback(callback: (nodeId: string, changes: any) => void): void;
  fit(): void;
  destroy(): void;
}

// 同步控制API接口
export interface SyncAPI {
  enableSync(enable: boolean): void;
  isSyncEnabled(): boolean;
  triggerSync(from: 'markdown' | 'mindmap'): Promise<void>;
  pauseSync(): void;
  resumeSync(): void;
  setDebounceTime(ms: number): void;
}

// AI服务API接口
export interface AIAPI {
  setProvider(provider: 'openai' | 'ollama'): void;
  configure(settings: Partial<MindMapSyncSettings>): Promise<boolean>;
  isConfigured(): boolean;
  ask(question: string, context: string): Promise<string>;
  cancelPendingRequest(): void;
}

// ==================== 逻辑连接相关类型定义 ====================

// 连接样式枚举
export type ConnectionStyle = 'solid' | 'dashed' | 'dotted' | 'bold';

// 箭头类型枚举
export type ArrowType = 'normal' | 'bold' | 'curved' | 'triangle';

// 连接颜色预设
export type ConnectionColor =
  | '#666666'  // 默认灰色
  | '#ff6b6b'  // 红色
  | '#4ecdc4'  // 青色
  | '#45b7d1'  // 蓝色
  | '#96ceb4'  // 绿色
  | '#feca57'  // 黄色
  | '#ff9ff3'  // 粉色
  | '#a55eea'; // 紫色

// 逻辑连接接口
export interface LogicalConnection {
  id: string;
  fromNodeId: string;
  toNodeId: string;
  type: 'logical';
  style: ConnectionStyle;
  color: string;
  width: number;
  label?: string;
  arrowType: ArrowType;
  createdAt: Date;
  updatedAt?: Date;
}

// 连接操作事件类型
export type ConnectionEventType =
  | 'connection:create'
  | 'connection:update'
  | 'connection:delete'
  | 'connection:select'
  | 'connection:hover';

// 连接操作事件数据
export interface ConnectionEventData {
  connectionId: string;
  connection?: LogicalConnection;
  changes?: Partial<LogicalConnection>;
  position?: { x: number; y: number };
}

// 连接模式状态
export type ConnectionMode = 'normal' | 'connecting' | 'editing';

// 连接创建状态
export interface ConnectionCreationState {
  mode: ConnectionMode;
  sourceNodeId: string | null;
  targetNodeId: string | null;
  currentStyle: ConnectionStyle;
  currentColor: string;
  currentWidth: number;
  currentArrowType: ArrowType;
}

// 连接管理器接口
export interface ConnectionManager {
  // 连接CRUD操作
  createConnection(fromNodeId: string, toNodeId: string, options?: Partial<LogicalConnection>): LogicalConnection;
  updateConnection(connectionId: string, updates: Partial<LogicalConnection>): boolean;
  deleteConnection(connectionId: string): boolean;
  getConnection(connectionId: string): LogicalConnection | null;
  getAllConnections(): LogicalConnection[];

  // 连接查询
  getConnectionsByNode(nodeId: string): LogicalConnection[];
  getConnectionsBetween(fromNodeId: string, toNodeId: string): LogicalConnection[];

  // 连接验证
  validateConnection(fromNodeId: string, toNodeId: string): boolean;
  isCircularConnection(fromNodeId: string, toNodeId: string): boolean;

  // 事件处理
  setConnectionEventCallback(callback: (event: ConnectionEventType, data: ConnectionEventData) => void): void;
}

// 扩展思维导图API接口，添加连接功能
export interface ExtendedMindMapAPI extends MindMapAPI {
  // 连接管理
  addLogicalConnection(connection: LogicalConnection): void;
  removeLogicalConnection(connectionId: string): void;
  updateLogicalConnection(connectionId: string, updates: Partial<LogicalConnection>): void;
  getLogicalConnections(): LogicalConnection[];

  // 连接样式
  setConnectionStyle(connectionId: string, style: ConnectionStyle): void;
  setConnectionColor(connectionId: string, color: string): void;
  setConnectionLabel(connectionId: string, label: string): void;

  // 连接模式
  setConnectionMode(mode: ConnectionMode): void;
  getConnectionMode(): ConnectionMode;

  // 连接事件
  setConnectionEventCallback(callback: (event: ConnectionEventType, data: ConnectionEventData) => void): void;
}
