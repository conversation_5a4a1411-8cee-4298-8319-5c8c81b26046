# 思维导图逻辑箭头功能 - 计划

## 3.1 总体实施策略

基于**方案二：增强连接系统**，将功能拆解为4个主要模块，采用渐进式开发策略，确保每个步骤都可以独立测试和验证。

## 3.2 详细实施步骤

### 3.2.1 步骤1：扩展数据结构和类型定义

**文件路径**：`src/types/index.ts`
**修改范围**：添加逻辑连接相关的接口定义
**预期结果**：完善的TypeScript类型支持

**具体任务**：
- 定义`LogicalConnection`接口
- 扩展`NodeData`接口支持连接信息
- 添加连接样式枚举类型
- 定义连接管理相关的事件类型

**代码结构**：
```typescript
interface LogicalConnection {
  id: string;
  fromNodeId: string;
  toNodeId: string;
  type: 'logical';
  style: ConnectionStyle;
  color: string;
  width: number;
  label?: string;
  arrowType: ArrowType;
}
```

### 3.2.2 步骤2：扩展MarkdownParser支持连接数据

**文件路径**：`src/core/MarkdownParser.ts`
**修改范围**：添加连接数据的解析和序列化功能
**预期结果**：支持从Markdown读取和保存连接信息

**具体任务**：
- 添加`parseConnections()`方法解析HTML注释中的连接数据
- 添加`serializeConnections()`方法将连接数据写入HTML注释
- 修改`parse()`方法集成连接数据解析
- 修改`toMarkdown()`方法集成连接数据序列化

**数据格式**：
```html
<!-- mindmap-connections: [
  {
    "id": "conn-1",
    "from": "node-1-2",
    "to": "node-2-3",
    "type": "logical",
    "style": "dashed",
    "color": "#ff6b6b",
    "width": 2,
    "label": "影响关系",
    "arrowType": "normal"
  }
] -->
```

### 3.2.3 步骤3：扩展VisJsRenderer支持逻辑连接

**文件路径**：`src/core/VisJsRenderer.ts`
**修改范围**：添加逻辑连接的渲染和管理功能
**预期结果**：思维导图中能够显示和管理逻辑连接

**具体任务**：
- 添加`logicalConnections`数据集管理逻辑连接
- 实现`addLogicalConnection()`方法
- 实现`removeLogicalConnection()`方法
- 实现`updateLogicalConnection()`方法
- 修改`getNetworkOptions()`配置逻辑连接样式
- 扩展事件监听器支持连接操作

**关键方法**：
```typescript
class VisJsRenderer {
  private logicalConnections: any; // vis-data DataSet
  
  addLogicalConnection(connection: LogicalConnection): void
  removeLogicalConnection(connectionId: string): void
  updateLogicalConnection(connectionId: string, updates: Partial<LogicalConnection>): void
  getLogicalConnectionStyle(connection: LogicalConnection): EdgeOptions
}
```

### 3.2.4 步骤4：创建连接工具栏组件

**文件路径**：`src/ui/ConnectionToolbar.ts`（新建文件）
**修改范围**：创建专门的连接工具栏组件
**预期结果**：用户可以通过工具栏管理连接模式和样式

**具体任务**：
- 创建`ConnectionToolbar`类
- 实现连接模式切换按钮
- 实现连接样式选择器
- 实现连接颜色选择器
- 实现连接标签输入框
- 添加连接操作的状态提示

**组件结构**：
```typescript
class ConnectionToolbar {
  private container: HTMLElement;
  private isConnectionMode: boolean = false;
  private currentStyle: ConnectionStyle = 'solid';
  private currentColor: string = '#666666';
  
  createToolbar(): HTMLElement
  toggleConnectionMode(): void
  setConnectionStyle(style: ConnectionStyle): void
  setConnectionColor(color: string): void
}
```

### 3.2.5 步骤5：扩展MindMapView集成连接功能

**文件路径**：`src/ui/MindMapView.ts`
**修改范围**：集成连接工具栏和连接交互逻辑
**预期结果**：完整的连接功能用户界面

**具体任务**：
- 集成`ConnectionToolbar`组件
- 实现连接模式的状态管理
- 实现节点选择逻辑（源节点→目标节点）
- 添加连接创建的用户反馈
- 实现连接的右键菜单（编辑/删除）
- 扩展`handleNodeChange()`支持连接操作

**交互流程**：
```typescript
// 连接创建流程
1. 用户点击"连接模式"按钮
2. 界面提示"请选择源节点"
3. 用户点击源节点，节点高亮
4. 界面提示"请选择目标节点"
5. 用户点击目标节点
6. 弹出连接样式选择对话框
7. 创建连接并保存
```

### 3.2.6 步骤6：实现连接管理功能

**文件路径**：`src/ui/ConnectionManager.ts`（新建文件）
**修改范围**：创建连接管理组件
**预期结果**：用户可以查看、编辑、删除现有连接

**具体任务**：
- 创建连接列表显示组件
- 实现连接编辑对话框
- 实现连接删除确认对话框
- 添加批量连接操作功能
- 实现连接搜索和过滤功能

### 3.2.7 步骤7：样式系统实现

**文件路径**：`styles.css`
**修改范围**：添加连接相关的CSS样式
**预期结果**：美观的连接工具栏和连接样式

**具体任务**：
- 设计连接工具栏样式
- 定义逻辑连接的视觉样式
- 实现连接模式的状态指示样式
- 添加连接悬停和选中效果
- 确保样式与Obsidian主题兼容

### 3.2.8 步骤8：测试和优化

**文件路径**：全项目
**修改范围**：功能测试和性能优化
**预期结果**：稳定可靠的连接功能

**具体任务**：
- 单元测试：数据结构和解析功能
- 集成测试：完整的连接创建流程
- 用户体验测试：交互流程优化
- 性能测试：大量连接的渲染性能
- 兼容性测试：不同主题和设备的兼容性

## 3.3 开发时间安排

| 步骤 | 任务 | 预估时间 | 累计时间 |
|------|------|----------|----------|
| 3.2.1 | 数据结构扩展 | 4小时 | 4小时 |
| 3.2.2 | MarkdownParser扩展 | 6小时 | 10小时 |
| 3.2.3 | VisJsRenderer扩展 | 8小时 | 18小时 |
| 3.2.4 | 连接工具栏组件 | 8小时 | 26小时 |
| 3.2.5 | MindMapView集成 | 6小时 | 32小时 |
| 3.2.6 | 连接管理功能 | 8小时 | 40小时 |
| 3.2.7 | 样式系统实现 | 4小时 | 44小时 |
| 3.2.8 | 测试和优化 | 6小时 | 50小时 |

**总开发时间**：50小时（约6-7个工作日）

## 3.4 风险控制措施

### 3.4.1 技术风险
- **数据兼容性**：确保新功能不影响现有数据
- **性能影响**：监控连接功能对渲染性能的影响
- **浏览器兼容性**：测试不同浏览器的兼容性

### 3.4.2 用户体验风险
- **学习成本**：提供清晰的操作指引
- **界面复杂度**：保持界面简洁，避免功能过载
- **操作错误**：提供撤销和错误恢复机制

### 3.4.3 回退方案
- 每个步骤都保持向后兼容
- 提供功能开关，可以禁用连接功能
- 保留原有的思维导图功能不受影响

## 3.5 验收标准

### 3.5.1 功能验收
- ✅ 用户可以在任意两个节点间创建逻辑连接
- ✅ 支持至少3种连接样式（实线、虚线、粗线）
- ✅ 支持连接颜色自定义
- ✅ 支持连接标签添加
- ✅ 连接信息能够保存到Markdown并正确恢复
- ✅ 提供连接管理功能（查看、编辑、删除）

### 3.5.2 性能验收
- ✅ 100个节点+50个连接的渲染时间<2秒
- ✅ 连接创建操作响应时间<500ms
- ✅ 内存使用增长<20%

### 3.5.3 兼容性验收
- ✅ 不影响现有思维导图功能
- ✅ 兼容Obsidian默认主题和主流第三方主题
- ✅ 支持Chrome、Firefox、Safari主流浏览器

## 3.6 下一步行动

计划制定完成，等待用户确认后将进入**[执行]**阶段，按照上述步骤逐一实施功能开发。
