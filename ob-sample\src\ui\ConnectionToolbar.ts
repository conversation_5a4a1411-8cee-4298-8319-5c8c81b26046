/**
 * 连接工具栏组件
 * 提供连接模式切换、样式选择等功能
 */

import { ConnectionStyle, ArrowType, ConnectionColor, ConnectionMode } from '../types';
import { Logger } from '../utils';

export interface ConnectionToolbarOptions {
  onModeChange?: (mode: ConnectionMode) => void;
  onStyleChange?: (style: ConnectionStyle) => void;
  onColorChange?: (color: string) => void;
  onWidthChange?: (width: number) => void;
  onArrowTypeChange?: (arrowType: ArrowType) => void;
  onLabelChange?: (label: string) => void;
}

export class ConnectionToolbar {
  private container: HTMLElement;
  private logger: Logger;
  private options: ConnectionToolbarOptions;
  
  // 当前状态
  private currentMode: ConnectionMode = 'normal';
  private currentStyle: ConnectionStyle = 'solid';
  private currentColor: string = '#666666';
  private currentWidth: number = 2;
  private currentArrowType: ArrowType = 'normal';
  private currentLabel: string = '';
  
  // UI元素
  private modeButton: HTMLButtonElement | null = null;
  private styleSelector: HTMLSelectElement | null = null;
  private colorSelector: HTMLSelectElement | null = null;
  private widthSlider: HTMLInputElement | null = null;
  private arrowTypeSelector: HTMLSelectElement | null = null;
  private labelInput: HTMLInputElement | null = null;
  private statusIndicator: HTMLElement | null = null;

  constructor(container: HTMLElement, logger: Logger, options: ConnectionToolbarOptions = {}) {
    this.container = container;
    this.logger = logger;
    this.options = options;
    
    this.createToolbar();
    this.setupEventListeners();
  }

  /**
   * 创建工具栏UI
   */
  private createToolbar(): void {
    // 清空容器
    this.container.innerHTML = '';
    this.container.className = 'connection-toolbar';

    // 创建主工具栏容器
    const toolbar = this.container.createDiv('toolbar-main');

    // 连接模式按钮
    this.createModeButton(toolbar);

    // 分隔符
    toolbar.createDiv('toolbar-separator');

    // 样式选择器
    this.createStyleSelector(toolbar);

    // 颜色选择器
    this.createColorSelector(toolbar);

    // 线宽滑块
    this.createWidthSlider(toolbar);

    // 箭头类型选择器
    this.createArrowTypeSelector(toolbar);

    // 分隔符
    toolbar.createDiv('toolbar-separator');

    // 标签输入框
    this.createLabelInput(toolbar);

    // 状态指示器
    this.createStatusIndicator(toolbar);

    // 初始状态设置
    this.updateToolbarState();
  }

  /**
   * 创建模式切换按钮
   */
  private createModeButton(parent: HTMLElement): void {
    const buttonContainer = parent.createDiv('toolbar-item');
    
    this.modeButton = buttonContainer.createEl('button', {
      text: '连接模式',
      cls: 'connection-mode-btn'
    });

    this.modeButton.addEventListener('click', () => {
      this.toggleConnectionMode();
    });
  }

  /**
   * 创建样式选择器
   */
  private createStyleSelector(parent: HTMLElement): void {
    const selectorContainer = parent.createDiv('toolbar-item');
    
    const label = selectorContainer.createEl('label', {
      text: '样式:',
      cls: 'toolbar-label'
    });

    this.styleSelector = selectorContainer.createEl('select', {
      cls: 'style-selector'
    });

    // 添加样式选项
    const styles: { value: ConnectionStyle; text: string }[] = [
      { value: 'solid', text: '实线' },
      { value: 'dashed', text: '虚线' },
      { value: 'dotted', text: '点线' },
      { value: 'bold', text: '粗线' }
    ];

    styles.forEach(style => {
      const option = this.styleSelector!.createEl('option', {
        value: style.value,
        text: style.text
      });
    });

    this.styleSelector.value = this.currentStyle;
  }

  /**
   * 创建颜色选择器
   */
  private createColorSelector(parent: HTMLElement): void {
    const selectorContainer = parent.createDiv('toolbar-item');
    
    const label = selectorContainer.createEl('label', {
      text: '颜色:',
      cls: 'toolbar-label'
    });

    this.colorSelector = selectorContainer.createEl('select', {
      cls: 'color-selector'
    });

    // 添加颜色选项
    const colors: { value: string; text: string }[] = [
      { value: '#666666', text: '默认灰' },
      { value: '#ff6b6b', text: '红色' },
      { value: '#4ecdc4', text: '青色' },
      { value: '#45b7d1', text: '蓝色' },
      { value: '#96ceb4', text: '绿色' },
      { value: '#feca57', text: '黄色' },
      { value: '#ff9ff3', text: '粉色' },
      { value: '#a55eea', text: '紫色' }
    ];

    colors.forEach(color => {
      const option = this.colorSelector!.createEl('option', {
        value: color.value,
        text: color.text
      });
      option.style.color = color.value;
    });

    this.colorSelector.value = this.currentColor;
  }

  /**
   * 创建线宽滑块
   */
  private createWidthSlider(parent: HTMLElement): void {
    const sliderContainer = parent.createDiv('toolbar-item');
    
    const label = sliderContainer.createEl('label', {
      text: '粗细:',
      cls: 'toolbar-label'
    });

    this.widthSlider = sliderContainer.createEl('input', {
      type: 'range',
      cls: 'width-slider'
    });

    this.widthSlider.min = '1';
    this.widthSlider.max = '5';
    this.widthSlider.step = '1';
    this.widthSlider.value = this.currentWidth.toString();

    // 显示当前值
    const valueDisplay = sliderContainer.createEl('span', {
      text: this.currentWidth.toString(),
      cls: 'width-value'
    });

    this.widthSlider.addEventListener('input', () => {
      const value = parseInt(this.widthSlider!.value);
      valueDisplay.textContent = value.toString();
      this.currentWidth = value;
      this.options.onWidthChange?.(value);
    });
  }

  /**
   * 创建箭头类型选择器
   */
  private createArrowTypeSelector(parent: HTMLElement): void {
    const selectorContainer = parent.createDiv('toolbar-item');
    
    const label = selectorContainer.createEl('label', {
      text: '箭头:',
      cls: 'toolbar-label'
    });

    this.arrowTypeSelector = selectorContainer.createEl('select', {
      cls: 'arrow-type-selector'
    });

    // 添加箭头类型选项
    const arrowTypes: { value: ArrowType; text: string }[] = [
      { value: 'normal', text: '普通' },
      { value: 'bold', text: '粗体' },
      { value: 'curved', text: '弯曲' },
      { value: 'triangle', text: '三角' }
    ];

    arrowTypes.forEach(arrowType => {
      const option = this.arrowTypeSelector!.createEl('option', {
        value: arrowType.value,
        text: arrowType.text
      });
    });

    this.arrowTypeSelector.value = this.currentArrowType;
  }

  /**
   * 创建标签输入框
   */
  private createLabelInput(parent: HTMLElement): void {
    const inputContainer = parent.createDiv('toolbar-item');
    
    const label = inputContainer.createEl('label', {
      text: '标签:',
      cls: 'toolbar-label'
    });

    this.labelInput = inputContainer.createEl('input', {
      type: 'text',
      placeholder: '连接标签',
      cls: 'label-input'
    });

    this.labelInput.value = this.currentLabel;
  }

  /**
   * 创建状态指示器
   */
  private createStatusIndicator(parent: HTMLElement): void {
    this.statusIndicator = parent.createDiv('status-indicator');
    this.updateStatusIndicator();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 样式选择器事件
    this.styleSelector?.addEventListener('change', () => {
      this.currentStyle = this.styleSelector!.value as ConnectionStyle;
      this.options.onStyleChange?.(this.currentStyle);
    });

    // 颜色选择器事件
    this.colorSelector?.addEventListener('change', () => {
      this.currentColor = this.colorSelector!.value;
      this.options.onColorChange?.(this.currentColor);
    });

    // 箭头类型选择器事件
    this.arrowTypeSelector?.addEventListener('change', () => {
      this.currentArrowType = this.arrowTypeSelector!.value as ArrowType;
      this.options.onArrowTypeChange?.(this.currentArrowType);
    });

    // 标签输入框事件
    this.labelInput?.addEventListener('input', () => {
      this.currentLabel = this.labelInput!.value;
      this.options.onLabelChange?.(this.currentLabel);
    });
  }

  /**
   * 切换连接模式
   */
  private toggleConnectionMode(): void {
    this.currentMode = this.currentMode === 'normal' ? 'connecting' : 'normal';
    this.updateToolbarState();
    this.options.onModeChange?.(this.currentMode);
  }

  /**
   * 更新工具栏状态
   */
  private updateToolbarState(): void {
    if (!this.modeButton) return;

    // 更新模式按钮
    this.modeButton.textContent = this.currentMode === 'connecting' ? '退出连接' : '连接模式';
    this.modeButton.className = this.currentMode === 'connecting' ? 
      'connection-mode-btn active' : 'connection-mode-btn';

    // 更新工具栏可用性
    const isConnecting = this.currentMode === 'connecting';
    this.styleSelector!.disabled = !isConnecting;
    this.colorSelector!.disabled = !isConnecting;
    this.widthSlider!.disabled = !isConnecting;
    this.arrowTypeSelector!.disabled = !isConnecting;
    this.labelInput!.disabled = !isConnecting;

    // 更新状态指示器
    this.updateStatusIndicator();
  }

  /**
   * 更新状态指示器
   */
  private updateStatusIndicator(): void {
    if (!this.statusIndicator) return;

    let statusText = '';
    let statusClass = '';

    switch (this.currentMode) {
      case 'normal':
        statusText = '正常模式';
        statusClass = 'status-normal';
        break;
      case 'connecting':
        statusText = '连接模式 - 点击两个节点创建连接';
        statusClass = 'status-connecting';
        break;
      case 'editing':
        statusText = '编辑模式';
        statusClass = 'status-editing';
        break;
    }

    this.statusIndicator.textContent = statusText;
    this.statusIndicator.className = `status-indicator ${statusClass}`;
  }

  // ==================== 公共API ====================

  /**
   * 设置连接模式
   */
  setMode(mode: ConnectionMode): void {
    this.currentMode = mode;
    this.updateToolbarState();
  }

  /**
   * 获取当前连接模式
   */
  getMode(): ConnectionMode {
    return this.currentMode;
  }

  /**
   * 获取当前连接样式配置
   */
  getCurrentConnectionConfig() {
    return {
      style: this.currentStyle,
      color: this.currentColor,
      width: this.currentWidth,
      arrowType: this.currentArrowType,
      label: this.currentLabel
    };
  }

  /**
   * 重置工具栏状态
   */
  reset(): void {
    this.currentMode = 'normal';
    this.currentStyle = 'solid';
    this.currentColor = '#666666';
    this.currentWidth = 2;
    this.currentArrowType = 'normal';
    this.currentLabel = '';
    
    this.updateToolbarState();
  }

  /**
   * 销毁工具栏
   */
  destroy(): void {
    this.container.innerHTML = '';
  }
}
