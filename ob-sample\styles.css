/*
智能思维导图同步插件样式文件
*/

/* 主题适配CSS变量 */
:root {
  /* 明亮主题思维导图颜色 */
  --mindmap-heading1-bg: #e3f2fd;
  --mindmap-heading1-border: #1976d2;
  --mindmap-heading1-text: #1565c0;

  --mindmap-heading2-bg: #f3e5f5;
  --mindmap-heading2-border: #7b1fa2;
  --mindmap-heading2-text: #6a1b9a;

  --mindmap-heading3-bg: #e8f5e8;
  --mindmap-heading3-border: #388e3c;
  --mindmap-heading3-text: #2e7d32;

  --mindmap-quote-bg: #fff3e0;
  --mindmap-quote-border: #f57c00;
  --mindmap-quote-text: #e65100;

  --mindmap-ai-bg: rgba(34, 197, 94, 0.1);
  --mindmap-ai-border: var(--color-green);
  --mindmap-ai-text: var(--text-normal);
}

/* 暗黑主题适配 */
.theme-dark {
  /* 暗黑主题思维导图颜色 */
  --mindmap-heading1-bg: rgba(33, 150, 243, 0.25);
  --mindmap-heading1-border: #42a5f5;
  --mindmap-heading1-text: #90caf9;

  --mindmap-heading2-bg: rgba(156, 39, 176, 0.25);
  --mindmap-heading2-border: #ab47bc;
  --mindmap-heading2-text: #ce93d8;

  --mindmap-heading3-bg: rgba(76, 175, 80, 0.25);
  --mindmap-heading3-border: #66bb6a;
  --mindmap-heading3-text: #a5d6a7;

  --mindmap-quote-bg: rgba(255, 152, 0, 0.25);
  --mindmap-quote-border: #ffb74d;
  --mindmap-quote-text: #ffcc02;

  --mindmap-ai-bg: rgba(34, 197, 94, 0.25);
  --mindmap-ai-border: var(--color-green);
  --mindmap-ai-text: #4ade80;
}

/* 思维导图节点主题适配样式类 */
.mindmap-node-heading1 {
  background: var(--mindmap-heading1-bg) !important;
  border-color: var(--mindmap-heading1-border) !important;
  color: var(--mindmap-heading1-text) !important;
}

.mindmap-node-heading2 {
  background: var(--mindmap-heading2-bg) !important;
  border-color: var(--mindmap-heading2-border) !important;
  color: var(--mindmap-heading2-text) !important;
}

.mindmap-node-heading3 {
  background: var(--mindmap-heading3-bg) !important;
  border-color: var(--mindmap-heading3-border) !important;
  color: var(--mindmap-heading3-text) !important;
}

.mindmap-node-quote {
  background: var(--mindmap-quote-bg) !important;
  border-color: var(--mindmap-quote-border) !important;
  color: var(--mindmap-quote-text) !important;
  font-style: italic;
}

.mindmap-node-ai {
  background: var(--mindmap-ai-bg) !important;
  border-color: var(--mindmap-ai-border) !important;
  color: var(--mindmap-ai-text) !important;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --mindmap-heading1-text: #000000;
    --mindmap-heading2-text: #000000;
    --mindmap-heading3-text: #000000;
    --mindmap-quote-text: #000000;
  }

  .theme-dark {
    --mindmap-heading1-text: #ffffff;
    --mindmap-heading2-text: #ffffff;
    --mindmap-heading3-text: #ffffff;
    --mindmap-quote-text: #ffffff;
    --mindmap-ai-text: #ffffff;
  }
}

/* 思维导图视图容器 */
.mindmap-view-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--background-primary);
}

/* 思维导图工具栏 */
.mindmap-toolbar {
  background: var(--background-secondary);
  border-bottom: 1px solid var(--background-modifier-border);
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.mindmap-toolbar button {
  padding: 4px 8px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-normal);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.mindmap-toolbar button:hover {
  background: var(--background-modifier-hover);
}

.mindmap-toolbar button.mod-cta {
  background: var(--interactive-accent);
  color: var(--text-on-accent);
  border-color: var(--interactive-accent);
}

.mindmap-toolbar .mindmap-file-label {
  margin-left: auto;
  font-size: 12px;
  color: var(--text-muted);
  font-style: italic;
}

/* 思维导图容器 */
.mindmap-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: var(--background-primary);
}

/* 思维导图节点样式 */
.mindmap-container .mm-node {
  cursor: pointer;
  transition: all 0.2s ease;
}

.mindmap-container .mm-node:hover {
  opacity: 0.8;
}

.mindmap-container .mm-node-highlighted {
  stroke: var(--interactive-accent) !important;
  stroke-width: 2px !important;
}

/* AI生成节点特殊样式 */
.mindmap-container .mm-node-ai {
  stroke: var(--color-green) !important;
  fill: var(--color-green-rgb) !important;
}

.mindmap-container .mm-node-ai::after {
  content: "🤖";
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 10px;
}

/* 思维导图链接样式 */
.mindmap-container .mm-link {
  stroke: var(--text-muted);
  stroke-width: 1.5px;
  fill: none;
}

/* 左侧面板图标 */
.mindmap-ribbon-icon {
  color: var(--text-normal);
}

.mindmap-ribbon-icon:hover {
  color: var(--interactive-accent);
}

/* 加载状态 */
.mindmap-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-muted);
  font-size: 14px;
}

.mindmap-loading::before {
  content: "";
  width: 20px;
  height: 20px;
  border: 2px solid var(--background-modifier-border);
  border-top: 2px solid var(--interactive-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.mindmap-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-error);
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

.mindmap-error .error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.mindmap-error .error-message {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.mindmap-error .error-detail {
  font-size: 12px;
  color: var(--text-muted);
}

/* 空状态 */
.mindmap-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-muted);
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

.mindmap-empty .empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.mindmap-empty .empty-message {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.mindmap-empty .empty-hint {
  font-size: 12px;
  color: var(--text-muted);
}

/* Vis.js网络图样式覆盖 */
.mindmap-container .vis-network {
  outline: none;
}

.mindmap-container .vis-network:focus {
  outline: none;
}

/* AI提问模态框样式 */
.ai-question-modal .modal-content {
  max-width: 500px;
}

.ai-question-modal .modal-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--text-normal);
}

.ai-question-modal .selected-text {
  background: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
  font-family: var(--font-monospace);
  font-size: 13px;
  color: var(--text-muted);
  max-height: 100px;
  overflow-y: auto;
}

.ai-question-modal .question-input {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-normal);
  font-family: var(--font-text);
  font-size: 14px;
  resize: vertical;
  margin-bottom: 16px;
}

.ai-question-modal .question-input:focus {
  outline: none;
  border-color: var(--interactive-accent);
}

.ai-question-modal .modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.ai-question-modal .modal-buttons button {
  padding: 8px 16px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-normal);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.ai-question-modal .modal-buttons button:hover {
  background: var(--background-modifier-hover);
}

.ai-question-modal .modal-buttons button.mod-cta {
  background: var(--interactive-accent);
  color: var(--text-on-accent);
  border-color: var(--interactive-accent);
}

/* AI回答样式 */
.ai-answer-block {
  border-left: 4px solid var(--color-green);
  background: var(--background-secondary);
  padding: 12px 16px;
  margin: 16px 0;
  border-radius: 0 4px 4px 0;
}

.ai-answer-block .ai-answer-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: var(--text-muted);
}

.ai-answer-block .ai-answer-header::before {
  content: "🤖";
  font-size: 14px;
}

.ai-answer-block .ai-answer-content {
  color: var(--text-normal);
  line-height: 1.6;
}

/* 设置页面样式 */
.mindmap-settings h2 {
  color: var(--text-normal);
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 16px;
  border-bottom: 1px solid var(--background-modifier-border);
  padding-bottom: 8px;
}

.mindmap-settings h3 {
  color: var(--text-normal);
  font-size: 16px;
  font-weight: 500;
  margin: 24px 0 12px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mindmap-toolbar {
    flex-wrap: wrap;
    gap: 4px;
  }

  .mindmap-toolbar button {
    font-size: 11px;
    padding: 3px 6px;
  }

  .ai-question-modal .modal-content {
    max-width: 90vw;
    margin: 20px;
  }
}

/* ==================== 连接功能样式 ==================== */

/* 连接工具栏样式 */
.connection-toolbar-container {
  border-bottom: 1px solid var(--background-modifier-border);
  background: var(--background-secondary);
  padding: 8px;
  min-height: 40px;
}

.connection-toolbar {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.toolbar-main {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.toolbar-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.toolbar-label {
  font-size: 12px;
  color: var(--text-muted);
  white-space: nowrap;
}

.toolbar-separator {
  width: 1px;
  height: 20px;
  background: var(--background-modifier-border);
  margin: 0 4px;
}

/* 连接模式按钮 */
.connection-mode-btn {
  padding: 4px 12px;
  border: 1px solid var(--interactive-accent);
  background: var(--background-primary);
  color: var(--interactive-accent);
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.connection-mode-btn:hover {
  background: var(--interactive-accent-hover);
  color: var(--text-on-accent);
}

.connection-mode-btn.active {
  background: var(--interactive-accent);
  color: var(--text-on-accent);
}

/* 样式选择器 */
.style-selector,
.color-selector,
.arrow-type-selector {
  padding: 2px 6px;
  border: 1px solid var(--background-modifier-border);
  background: var(--background-primary);
  color: var(--text-normal);
  border-radius: 3px;
  font-size: 12px;
  min-width: 80px;
}

.style-selector:disabled,
.color-selector:disabled,
.arrow-type-selector:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 线宽滑块 */
.width-slider {
  width: 60px;
  height: 4px;
  background: var(--background-modifier-border);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.width-slider::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  background: var(--interactive-accent);
  border-radius: 50%;
  cursor: pointer;
}

.width-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: var(--interactive-accent);
  border-radius: 50%;
  border: none;
  cursor: pointer;
}

.width-value {
  font-size: 11px;
  color: var(--text-muted);
  min-width: 12px;
  text-align: center;
}

/* 标签输入框 */
.label-input {
  padding: 2px 6px;
  border: 1px solid var(--background-modifier-border);
  background: var(--background-primary);
  color: var(--text-normal);
  border-radius: 3px;
  font-size: 12px;
  width: 120px;
}

.label-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 状态指示器 */
.status-indicator {
  font-size: 11px;
  padding: 2px 8px;
  border-radius: 10px;
  margin-left: auto;
}

.status-normal {
  background: var(--background-modifier-success);
  color: var(--text-success);
}

.status-connecting {
  background: var(--background-modifier-error);
  color: var(--text-error);
  animation: pulse 1.5s infinite;
}

.status-editing {
  background: var(--background-modifier-cover);
  color: var(--text-accent);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* 连接模式状态样式 */
.mindmap-container.connection-mode-normal {
  cursor: default;
}

.mindmap-container.connection-mode-connecting {
  cursor: crosshair;
}

.mindmap-container.connection-mode-connecting::before {
  content: "连接模式：点击两个节点创建连接";
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--background-modifier-error);
  color: var(--text-error);
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  pointer-events: none;
}

.mindmap-container.connection-mode-editing {
  cursor: pointer;
}

/* 连接管理器样式 */
.connection-manager {
  background: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 6px;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.manager-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--background-modifier-border);
}

.manager-title {
  margin: 0;
  font-size: 16px;
  color: var(--text-normal);
}

.connection-count {
  font-size: 12px;
  color: var(--text-muted);
  background: var(--background-secondary);
  padding: 2px 6px;
  border-radius: 10px;
}

/* 搜索和过滤栏 */
.search-filter-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.search-container,
.filter-container {
  display: flex;
  align-items: center;
  gap: 6px;
}

.search-label,
.filter-label {
  font-size: 12px;
  color: var(--text-muted);
  white-space: nowrap;
}

.search-input {
  padding: 4px 8px;
  border: 1px solid var(--background-modifier-border);
  background: var(--background-primary);
  color: var(--text-normal);
  border-radius: 4px;
  font-size: 12px;
  width: 200px;
}

.filter-select {
  padding: 4px 8px;
  border: 1px solid var(--background-modifier-border);
  background: var(--background-primary);
  color: var(--text-normal);
  border-radius: 4px;
  font-size: 12px;
  min-width: 80px;
}

/* 连接列表 */
.connection-list {
  margin-bottom: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.connection-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  margin-bottom: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.connection-item:hover {
  background: var(--background-modifier-hover);
  border-color: var(--interactive-accent);
}

.connection-item.selected {
  background: var(--interactive-accent-hover);
  border-color: var(--interactive-accent);
}

.connection-info {
  flex: 1;
}

.connection-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-normal);
  margin-bottom: 4px;
}

.connection-details {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.detail-item {
  font-size: 11px;
  color: var(--text-muted);
  background: var(--background-secondary);
  padding: 1px 4px;
  border-radius: 2px;
}

.connection-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  padding: 2px 6px;
  border: 1px solid var(--background-modifier-border);
  background: var(--background-primary);
  color: var(--text-normal);
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--background-modifier-hover);
}

.edit-btn:hover {
  border-color: var(--interactive-accent);
  color: var(--interactive-accent);
}

.delete-btn:hover {
  border-color: var(--text-error);
  color: var(--text-error);
}

.empty-message {
  text-align: center;
  color: var(--text-muted);
  font-size: 12px;
  padding: 20px;
}

/* 操作按钮栏 */
.action-bar {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 8px;
  border-top: 1px solid var(--background-modifier-border);
}

.refresh-btn {
  background: var(--interactive-accent);
  color: var(--text-on-accent);
  border: none;
}

.clear-all-btn {
  background: var(--text-error);
  color: white;
  border: none;
}

/* 连接编辑模态框样式 */
.connection-edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.connection-edit-modal .modal-content {
  background: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  padding: 24px;
  min-width: 400px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.connection-edit-modal .modal-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: var(--text-normal);
}

.edit-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-normal);
}

.form-input,
.form-select {
  padding: 8px 12px;
  border: 1px solid var(--background-modifier-border);
  background: var(--background-primary);
  color: var(--text-normal);
  border-radius: 4px;
  font-size: 13px;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: var(--interactive-accent);
  box-shadow: 0 0 0 2px var(--interactive-accent-hover);
}

.form-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 8px;
}

.btn-primary {
  padding: 8px 16px;
  background: var(--interactive-accent);
  color: var(--text-on-accent);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: background 0.2s ease;
}

.btn-primary:hover {
  background: var(--interactive-accent-hover);
}

.btn-secondary {
  padding: 8px 16px;
  background: var(--background-secondary);
  color: var(--text-normal);
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: var(--background-modifier-hover);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .connection-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .toolbar-main {
    width: 100%;
    justify-content: space-between;
  }

  .search-filter-bar {
    flex-direction: column;
    gap: 8px;
  }

  .search-input {
    width: 100%;
  }

  .connection-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .connection-actions {
    align-self: flex-end;
  }

  .connection-edit-modal .modal-content {
    min-width: 300px;
    margin: 20px;
  }

  .form-buttons {
    flex-direction: column;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
  }
}
