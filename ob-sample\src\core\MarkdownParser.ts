/**
 * Markdown文档解析器
 * 负责解析Markdown文档结构，生成节点树
 */

import { NodeData, Position, LogicalConnection } from '../types';
import {
  generateId,
  parseHeadingLevel,
  parseListLevel,
  isCodeBlock,
  cleanMarkdown,
  Logger
} from '../utils';

export class MarkdownParser {
  private logger: Logger;
  private connections: LogicalConnection[] = [];

  constructor(logger: Logger) {
    this.logger = logger;
  }
  
  /**
   * 解析Markdown文档，生成节点树
   */
  parse(content: string): NodeData[] {
    this.logger.debug('开始解析Markdown文档');

    // 🔥 智能检测连接数据并记录位置（不删除原始内容）
    const { connections, commentRanges } = this.detectConnectionComments(content);
    this.connections = connections;

    const lines = content.split('\n');
    const nodes: NodeData[] = [];
    const stack: NodeData[] = [];
    let inCodeBlock = false;
    let codeBlockNode: NodeData | null = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // 跳过空行
      if (!trimmedLine) {
        continue;
      }

      // 🔥 关键优化：智能跳过连接注释行
      if (this.isLineInConnectionComment(i, commentRanges)) {
        this.logger.debug(`跳过连接注释行: ${i + 1}`);
        continue;
      }
      
      // 处理代码块
      if (isCodeBlock(trimmedLine)) {
        if (!inCodeBlock) {
          // 开始代码块
          inCodeBlock = true;
          codeBlockNode = {
            id: generateId(),
            content: trimmedLine,
            level: 0,
            type: 'code',
            children: [],
            position: { line: i, ch: 0 }
          };
        } else {
          // 结束代码块
          if (codeBlockNode) {
            codeBlockNode.content += '\n' + trimmedLine;
            this.addNodeToTree(nodes, stack, codeBlockNode);
          }
          inCodeBlock = false;
          codeBlockNode = null;
        }
        continue;
      }
      
      // 在代码块内部
      if (inCodeBlock && codeBlockNode) {
        codeBlockNode.content += '\n' + line;
        continue;
      }
      
      // 解析标题
      const headingLevel = parseHeadingLevel(line);
      if (headingLevel > 0) {
        const node: NodeData = {
          id: generateId(),
          content: cleanMarkdown(line),
          level: headingLevel,
          type: 'heading',
          children: [],
          position: { line: i, ch: 0 }
        };
        
        this.addNodeToTree(nodes, stack, node);
        continue;
      }
      
      // 解析列表
      const listLevel = parseListLevel(line);
      if (listLevel > 0) {
        const node: NodeData = {
          id: generateId(),
          content: cleanMarkdown(line),
          level: listLevel,
          type: 'list',
          children: [],
          position: { line: i, ch: 0 }
        };
        
        this.addNodeToTree(nodes, stack, node);
        continue;
      }
      
      // 处理普通段落（作为前一个节点的子节点）
      if (trimmedLine && stack.length > 0) {
        const parentNode = stack[stack.length - 1];
        const paragraphNode: NodeData = {
          id: generateId(),
          content: trimmedLine,
          level: parentNode.level + 1,
          type: 'list',
          children: [],
          position: { line: i, ch: 0 }
        };
        
        parentNode.children.push(paragraphNode);
      }
    }
    
    this.logger.debug(`解析完成，生成 ${nodes.length} 个根节点`);
    return nodes;
  }
  
  /**
   * 将节点添加到树结构中
   */
  private addNodeToTree(nodes: NodeData[], stack: NodeData[], node: NodeData): void {
    // 清理栈，移除层级大于等于当前节点的节点
    while (stack.length > 0 && stack[stack.length - 1].level >= node.level) {
      stack.pop();
    }
    
    if (stack.length === 0) {
      // 根节点
      nodes.push(node);
    } else {
      // 子节点
      const parent = stack[stack.length - 1];
      parent.children.push(node);
    }
    
    stack.push(node);
  }
  
  /**
   * 将节点树转换回Markdown文本
   */
  toMarkdown(nodes: NodeData[]): string {
    const lines: string[] = [];

    for (const node of nodes) {
      this.nodeToMarkdown(node, lines);
    }

    // 添加连接数据注释
    const connectionComment = this.serializeConnections();
    if (connectionComment) {
      lines.push('');
      lines.push(connectionComment);
    }

    return lines.join('\n');
  }
  
  /**
   * 将单个节点转换为Markdown行
   */
  private nodeToMarkdown(node: NodeData, lines: string[]): void {
    let prefix = '';
    
    switch (node.type) {
      case 'heading':
        prefix = '#'.repeat(node.level) + ' ';
        break;
      case 'list':
        const indent = '  '.repeat(Math.max(0, node.level - 1));
        prefix = indent + '- ';
        break;
      case 'code':
        lines.push(node.content);
        return;
    }
    
    lines.push(prefix + node.content);
    
    // 递归处理子节点
    for (const child of node.children) {
      this.nodeToMarkdown(child, lines);
    }
  }
  
  /**
   * 查找指定位置的节点
   */
  findNodeByPosition(nodes: NodeData[], position: Position): NodeData | null {
    for (const node of nodes) {
      if (node.position && 
          node.position.line === position.line) {
        return node;
      }
      
      const childResult = this.findNodeByPosition(node.children, position);
      if (childResult) {
        return childResult;
      }
    }
    
    return null;
  }
  
  /**
   * 获取节点的路径
   */
  getNodePath(nodes: NodeData[], targetId: string): NodeData[] {
    const path: NodeData[] = [];
    
    const findPath = (currentNodes: NodeData[], currentPath: NodeData[]): boolean => {
      for (const node of currentNodes) {
        const newPath = [...currentPath, node];
        
        if (node.id === targetId) {
          path.push(...newPath);
          return true;
        }
        
        if (findPath(node.children, newPath)) {
          return true;
        }
      }
      return false;
    };
    
    findPath(nodes, []);
    return path;
  }
  
  /**
   * 计算节点树的差异
   */
  calculateDiff(oldNodes: NodeData[], newNodes: NodeData[]): {
    added: NodeData[];
    removed: NodeData[];
    modified: NodeData[];
  } {
    const added: NodeData[] = [];
    const removed: NodeData[] = [];
    const modified: NodeData[] = [];
    
    const oldNodeMap = new Map<string, NodeData>();
    const newNodeMap = new Map<string, NodeData>();
    
    // 构建节点映射
    this.buildNodeMap(oldNodes, oldNodeMap);
    this.buildNodeMap(newNodes, newNodeMap);
    
    // 查找新增和修改的节点
    for (const [id, newNode] of newNodeMap) {
      const oldNode = oldNodeMap.get(id);
      if (!oldNode) {
        added.push(newNode);
      } else if (oldNode.content !== newNode.content || 
                 oldNode.level !== newNode.level) {
        modified.push(newNode);
      }
    }
    
    // 查找删除的节点
    for (const [id, oldNode] of oldNodeMap) {
      if (!newNodeMap.has(id)) {
        removed.push(oldNode);
      }
    }
    
    return { added, removed, modified };
  }
  
  /**
   * 构建节点ID映射
   */
  private buildNodeMap(nodes: NodeData[], map: Map<string, NodeData>): void {
    for (const node of nodes) {
      map.set(node.id, node);
      this.buildNodeMap(node.children, map);
    }
  }

  // ==================== 连接数据解析和序列化方法 ====================

  /**
   * 智能检测连接注释并返回位置信息（不删除原始内容）
   */
  private detectConnectionComments(content: string): {
    connections: LogicalConnection[];
    commentRanges: Array<{start: number, end: number}>;
  } {
    const connections: LogicalConnection[] = [];
    const commentRanges: Array<{start: number, end: number}> = [];

    // 支持多行的正则表达式，匹配完整的连接注释块
    const connectionRegex = /<!--\s*mindmap-connections:\s*(\[[\s\S]*?\])\s*-->/g;

    let match;
    while ((match = connectionRegex.exec(content)) !== null) {
      try {
        // 解析连接数据
        const connectionsData = JSON.parse(match[1]);
        if (Array.isArray(connectionsData)) {
          for (const connData of connectionsData) {
            if (this.isValidConnectionData(connData)) {
              connections.push({
                ...connData,
                createdAt: connData.createdAt ? new Date(connData.createdAt) : new Date(),
                updatedAt: connData.updatedAt ? new Date(connData.updatedAt) : undefined
              });
            }
          }
        }

        // 计算注释在文档中的行号范围
        const beforeMatch = content.substring(0, match.index);
        const startLine = beforeMatch.split('\n').length - 1;
        const endLine = startLine + match[0].split('\n').length - 1;

        commentRanges.push({ start: startLine, end: endLine });

        this.logger.debug(`检测到连接注释: 行 ${startLine + 1}-${endLine + 1}, 包含 ${connectionsData.length} 个连接`);

      } catch (error) {
        this.logger.error('解析连接数据失败', error);
      }
    }

    this.logger.debug(`总共检测到 ${connections.length} 个连接, ${commentRanges.length} 个注释块`);
    return { connections, commentRanges };
  }

  /**
   * 检查当前行是否在连接注释范围内
   */
  private isLineInConnectionComment(
    lineIndex: number,
    commentRanges: Array<{start: number, end: number}>
  ): boolean {
    return commentRanges.some(range =>
      lineIndex >= range.start && lineIndex <= range.end
    );
  }



  /**
   * 验证连接数据的有效性
   * 支持必需字段和可选字段的灵活验证
   */
  private isValidConnectionData(data: any): boolean {
    // 验证基本结构
    if (!data || typeof data !== 'object') {
      this.logger.debug('连接数据验证失败：数据不是对象', data);
      return false;
    }

    // 验证必需字段
    const requiredFields = ['id', 'fromNodeId', 'toNodeId'];
    for (const field of requiredFields) {
      if (!data[field] || typeof data[field] !== 'string') {
        this.logger.debug(`连接数据验证失败：缺少或无效的必需字段 ${field}`, data);
        return false;
      }
    }

    // 验证可选字段的类型（如果存在）
    const optionalFields = {
      type: 'string',
      style: 'string',
      color: 'string',
      arrowType: 'string',
      width: 'number'
    };

    for (const [field, expectedType] of Object.entries(optionalFields)) {
      if (data[field] !== undefined && typeof data[field] !== expectedType) {
        this.logger.debug(`连接数据验证失败：字段 ${field} 类型错误，期望 ${expectedType}，实际 ${typeof data[field]}`, data);
        return false;
      }
    }

    this.logger.debug('连接数据验证通过', data);
    return true;
  }



  /**
   * 序列化连接数据到Markdown注释
   */
  private serializeConnections(): string {
    if (this.connections.length === 0) {
      return '';
    }

    const connectionsData = this.connections.map(conn => ({
      id: conn.id,
      fromNodeId: conn.fromNodeId,
      toNodeId: conn.toNodeId,
      type: conn.type,
      style: conn.style,
      color: conn.color,
      width: conn.width,
      label: conn.label,
      arrowType: conn.arrowType,
      createdAt: conn.createdAt.toISOString(),
      updatedAt: conn.updatedAt?.toISOString()
    }));

    return `<!-- mindmap-connections: ${JSON.stringify(connectionsData, null, 0)} -->`;
  }

  /**
   * 获取当前的连接数据
   */
  getConnections(): LogicalConnection[] {
    return [...this.connections];
  }

  /**
   * 设置连接数据
   */
  setConnections(connections: LogicalConnection[]): void {
    this.connections = [...connections];
  }

  /**
   * 添加连接
   */
  addConnection(connection: LogicalConnection): void {
    this.connections.push(connection);
  }

  /**
   * 删除连接
   */
  removeConnection(connectionId: string): boolean {
    const index = this.connections.findIndex(conn => conn.id === connectionId);
    if (index !== -1) {
      this.connections.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 更新连接
   */
  updateConnection(connectionId: string, updates: Partial<LogicalConnection>): boolean {
    const connection = this.connections.find(conn => conn.id === connectionId);
    if (connection) {
      Object.assign(connection, updates);
      connection.updatedAt = new Date();
      return true;
    }
    return false;
  }
}
