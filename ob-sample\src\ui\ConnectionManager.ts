/**
 * 连接管理器组件
 * 提供连接的查看、编辑、删除等管理功能
 */

import { LogicalConnection, ConnectionStyle, ArrowType, ExtendedMindMapAPI } from '../types';
import { Logger } from '../utils';

export interface ConnectionManagerOptions {
  onConnectionUpdate?: (connection: LogicalConnection) => void;
  onConnectionDelete?: (connectionId: string) => void;
  onConnectionSelect?: (connectionId: string) => void;
}

export class ConnectionManager {
  private container: HTMLElement;
  private logger: Logger;
  private renderer: ExtendedMindMapAPI;
  private options: ConnectionManagerOptions;
  
  // UI元素
  private connectionList: HTMLElement | null = null;
  private searchInput: HTMLInputElement | null = null;
  private filterSelect: HTMLSelectElement | null = null;
  private editModal: HTMLElement | null = null;
  
  // 状态
  private connections: LogicalConnection[] = [];
  private filteredConnections: LogicalConnection[] = [];
  private selectedConnectionId: string | null = null;

  constructor(
    container: HTMLElement, 
    renderer: ExtendedMindMapAPI, 
    logger: Logger, 
    options: ConnectionManagerOptions = {}
  ) {
    this.container = container;
    this.renderer = renderer;
    this.logger = logger;
    this.options = options;
    
    this.createManagerUI();
    this.refreshConnections();
  }

  /**
   * 创建管理器UI
   */
  private createManagerUI(): void {
    this.container.innerHTML = '';
    this.container.className = 'connection-manager';

    // 创建标题栏
    this.createHeader();

    // 创建搜索和过滤栏
    this.createSearchAndFilter();

    // 创建连接列表
    this.createConnectionList();

    // 创建操作按钮栏
    this.createActionBar();
  }

  /**
   * 创建标题栏
   */
  private createHeader(): void {
    const header = this.container.createDiv('manager-header');
    
    const title = header.createEl('h3', {
      text: '连接管理',
      cls: 'manager-title'
    });

    const connectionCount = header.createEl('span', {
      text: `(${this.connections.length})`,
      cls: 'connection-count'
    });
  }

  /**
   * 创建搜索和过滤栏
   */
  private createSearchAndFilter(): void {
    const searchBar = this.container.createDiv('search-filter-bar');

    // 搜索输入框
    const searchContainer = searchBar.createDiv('search-container');
    searchContainer.createEl('label', {
      text: '搜索:',
      cls: 'search-label'
    });

    this.searchInput = searchContainer.createEl('input', {
      type: 'text',
      placeholder: '搜索连接标签或节点...',
      cls: 'search-input'
    });

    this.searchInput.addEventListener('input', () => {
      this.filterConnections();
    });

    // 过滤选择器
    const filterContainer = searchBar.createDiv('filter-container');
    filterContainer.createEl('label', {
      text: '过滤:',
      cls: 'filter-label'
    });

    this.filterSelect = filterContainer.createEl('select', {
      cls: 'filter-select'
    });

    // 添加过滤选项
    const filterOptions = [
      { value: 'all', text: '全部' },
      { value: 'solid', text: '实线' },
      { value: 'dashed', text: '虚线' },
      { value: 'dotted', text: '点线' },
      { value: 'bold', text: '粗线' }
    ];

    filterOptions.forEach(option => {
      this.filterSelect!.createEl('option', {
        value: option.value,
        text: option.text
      });
    });

    this.filterSelect.addEventListener('change', () => {
      this.filterConnections();
    });
  }

  /**
   * 创建连接列表
   */
  private createConnectionList(): void {
    this.connectionList = this.container.createDiv('connection-list');
  }

  /**
   * 创建操作按钮栏
   */
  private createActionBar(): void {
    const actionBar = this.container.createDiv('action-bar');

    // 刷新按钮
    const refreshBtn = actionBar.createEl('button', {
      text: '刷新',
      cls: 'action-btn refresh-btn'
    });
    refreshBtn.addEventListener('click', () => {
      this.refreshConnections();
    });

    // 删除选中按钮
    const deleteBtn = actionBar.createEl('button', {
      text: '删除选中',
      cls: 'action-btn delete-btn'
    });
    deleteBtn.addEventListener('click', () => {
      this.deleteSelectedConnection();
    });

    // 清空所有按钮
    const clearAllBtn = actionBar.createEl('button', {
      text: '清空所有',
      cls: 'action-btn clear-all-btn'
    });
    clearAllBtn.addEventListener('click', () => {
      this.clearAllConnections();
    });
  }

  /**
   * 刷新连接列表
   */
  refreshConnections(): void {
    this.connections = this.renderer.getLogicalConnections();
    this.filterConnections();
    this.updateConnectionCount();
  }

  /**
   * 过滤连接
   */
  private filterConnections(): void {
    const searchTerm = this.searchInput?.value.toLowerCase() || '';
    const filterStyle = this.filterSelect?.value || 'all';

    this.filteredConnections = this.connections.filter(connection => {
      // 搜索过滤
      const matchesSearch = !searchTerm || 
        connection.label?.toLowerCase().includes(searchTerm) ||
        connection.fromNodeId.toLowerCase().includes(searchTerm) ||
        connection.toNodeId.toLowerCase().includes(searchTerm);

      // 样式过滤
      const matchesFilter = filterStyle === 'all' || connection.style === filterStyle;

      return matchesSearch && matchesFilter;
    });

    this.renderConnectionList();
  }

  /**
   * 渲染连接列表
   */
  private renderConnectionList(): void {
    if (!this.connectionList) return;

    this.connectionList.innerHTML = '';

    if (this.filteredConnections.length === 0) {
      const emptyMessage = this.connectionList.createDiv('empty-message');
      emptyMessage.textContent = '没有找到连接';
      return;
    }

    this.filteredConnections.forEach(connection => {
      this.renderConnectionItem(connection);
    });
  }

  /**
   * 渲染单个连接项
   */
  private renderConnectionItem(connection: LogicalConnection): void {
    if (!this.connectionList) return;

    const item = this.connectionList.createDiv('connection-item');
    item.dataset.connectionId = connection.id;

    // 选中状态
    if (connection.id === this.selectedConnectionId) {
      item.addClass('selected');
    }

    // 连接信息
    const info = item.createDiv('connection-info');
    
    // 连接标题
    const title = info.createDiv('connection-title');
    title.textContent = connection.label || `连接 ${connection.id.substring(0, 8)}`;

    // 连接详情
    const details = info.createDiv('connection-details');
    details.innerHTML = `
      <span class="detail-item">从: ${this.getNodeLabel(connection.fromNodeId)}</span>
      <span class="detail-item">到: ${this.getNodeLabel(connection.toNodeId)}</span>
      <span class="detail-item">样式: ${this.getStyleLabel(connection.style)}</span>
      <span class="detail-item" style="color: ${connection.color}">颜色: ${connection.color}</span>
    `;

    // 操作按钮
    const actions = item.createDiv('connection-actions');

    const editBtn = actions.createEl('button', {
      text: '编辑',
      cls: 'action-btn edit-btn'
    });
    editBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.editConnection(connection);
    });

    const deleteBtn = actions.createEl('button', {
      text: '删除',
      cls: 'action-btn delete-btn'
    });
    deleteBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.deleteConnection(connection.id);
    });

    // 点击选中
    item.addEventListener('click', () => {
      this.selectConnection(connection.id);
    });
  }

  /**
   * 获取节点标签
   */
  private getNodeLabel(nodeId: string): string {
    // 这里可以从渲染器获取节点的实际标签
    // 暂时返回节点ID的简化版本
    return nodeId.substring(0, 12) + '...';
  }

  /**
   * 获取样式标签
   */
  private getStyleLabel(style: ConnectionStyle): string {
    const styleLabels: Record<ConnectionStyle, string> = {
      'solid': '实线',
      'dashed': '虚线',
      'dotted': '点线',
      'bold': '粗线'
    };
    return styleLabels[style] || style;
  }

  /**
   * 选中连接
   */
  private selectConnection(connectionId: string): void {
    // 更新选中状态
    this.selectedConnectionId = connectionId;
    
    // 更新UI
    this.connectionList?.querySelectorAll('.connection-item').forEach(item => {
      item.classList.remove('selected');
    });
    
    const selectedItem = this.connectionList?.querySelector(`[data-connection-id="${connectionId}"]`);
    selectedItem?.classList.add('selected');

    // 通知外部
    this.options.onConnectionSelect?.(connectionId);
  }

  /**
   * 编辑连接
   */
  private editConnection(connection: LogicalConnection): void {
    this.showEditModal(connection);
  }

  /**
   * 显示编辑模态框
   */
  private showEditModal(connection: LogicalConnection): void {
    // 创建模态框
    this.editModal = document.body.createDiv('connection-edit-modal');
    
    const modalContent = this.editModal.createDiv('modal-content');
    
    // 标题
    const title = modalContent.createEl('h3', {
      text: '编辑连接',
      cls: 'modal-title'
    });

    // 表单
    const form = modalContent.createEl('form', {
      cls: 'edit-form'
    });

    // 标签输入
    const labelGroup = form.createDiv('form-group');
    labelGroup.createEl('label', { text: '标签:' });
    const labelInput = labelGroup.createEl('input', {
      type: 'text',
      value: connection.label || '',
      cls: 'form-input'
    });

    // 样式选择
    const styleGroup = form.createDiv('form-group');
    styleGroup.createEl('label', { text: '样式:' });
    const styleSelect = styleGroup.createEl('select', { cls: 'form-select' });
    
    const styles: ConnectionStyle[] = ['solid', 'dashed', 'dotted', 'bold'];
    styles.forEach(style => {
      const option = styleSelect.createEl('option', {
        value: style,
        text: this.getStyleLabel(style)
      });
      if (style === connection.style) {
        option.selected = true;
      }
    });

    // 颜色选择
    const colorGroup = form.createDiv('form-group');
    colorGroup.createEl('label', { text: '颜色:' });
    const colorInput = colorGroup.createEl('input', {
      type: 'color',
      value: connection.color,
      cls: 'form-input'
    });

    // 按钮
    const buttonGroup = form.createDiv('form-buttons');
    
    const saveBtn = buttonGroup.createEl('button', {
      text: '保存',
      type: 'submit',
      cls: 'btn-primary'
    });

    const cancelBtn = buttonGroup.createEl('button', {
      text: '取消',
      type: 'button',
      cls: 'btn-secondary'
    });

    // 事件处理
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.saveConnectionEdit(connection, {
        label: labelInput.value,
        style: styleSelect.value as ConnectionStyle,
        color: colorInput.value
      });
    });

    cancelBtn.addEventListener('click', () => {
      this.closeEditModal();
    });

    // 点击背景关闭
    this.editModal.addEventListener('click', (e) => {
      if (e.target === this.editModal) {
        this.closeEditModal();
      }
    });
  }

  /**
   * 保存连接编辑
   */
  private saveConnectionEdit(connection: LogicalConnection, updates: Partial<LogicalConnection>): void {
    this.renderer.updateLogicalConnection(connection.id, updates);
    this.options.onConnectionUpdate?.(connection);
    this.refreshConnections();
    this.closeEditModal();
  }

  /**
   * 关闭编辑模态框
   */
  private closeEditModal(): void {
    if (this.editModal) {
      this.editModal.remove();
      this.editModal = null;
    }
  }

  /**
   * 删除连接
   */
  private deleteConnection(connectionId: string): void {
    if (confirm('确定要删除这个连接吗？')) {
      this.renderer.removeLogicalConnection(connectionId);
      this.options.onConnectionDelete?.(connectionId);
      this.refreshConnections();
      
      if (this.selectedConnectionId === connectionId) {
        this.selectedConnectionId = null;
      }
    }
  }

  /**
   * 删除选中的连接
   */
  private deleteSelectedConnection(): void {
    if (this.selectedConnectionId) {
      this.deleteConnection(this.selectedConnectionId);
    }
  }

  /**
   * 清空所有连接
   */
  private clearAllConnections(): void {
    if (confirm('确定要删除所有连接吗？此操作不可撤销。')) {
      this.connections.forEach(connection => {
        this.renderer.removeLogicalConnection(connection.id);
      });
      this.refreshConnections();
      this.selectedConnectionId = null;
    }
  }

  /**
   * 更新连接数量显示
   */
  private updateConnectionCount(): void {
    const countElement = this.container.querySelector('.connection-count');
    if (countElement) {
      countElement.textContent = `(${this.connections.length})`;
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.closeEditModal();
    this.container.innerHTML = '';
  }
}
