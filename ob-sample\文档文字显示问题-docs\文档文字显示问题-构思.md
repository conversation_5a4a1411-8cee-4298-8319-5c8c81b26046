# 文档文字显示问题 - 构思

## 方案概述

针对思维导图文字被颜色覆盖、对比度不足的问题，提出3个可行解决方案。

## 方案一：智能主题适配方案（推荐）

### 1.1 方案描述
- 动态检测Obsidian当前主题（明亮/暗黑）
- 根据主题自动调整文字颜色和背景色
- 使用Obsidian原生CSS变量确保兼容性
- 保持高对比度，确保文字清晰可读

### 1.2 技术实现
```typescript
// 动态主题检测
private getThemeAwareColors() {
  const isDark = document.body.classList.contains('theme-dark');
  return {
    heading1: {
      background: isDark ? 'rgba(33, 150, 243, 0.2)' : '#e3f2fd',
      text: isDark ? '#90caf9' : '#1565c0',
      border: isDark ? '#42a5f5' : '#1976d2'
    },
    // ... 其他节点类型
  };
}
```

### 1.3 优缺点分析
**优点**：
- 完美适配所有Obsidian主题
- 自动响应主题切换
- 保证最佳可读性
- 代码维护性好

**缺点**：
- 需要监听主题变化事件
- 实现相对复杂

### 1.4 工作量评估
- 开发时间：2-3小时
- 测试时间：1小时
- 总计：3-4小时

## 方案二：高对比度安全配色方案

### 2.1 方案描述
- 使用经过验证的高对比度颜色组合
- 采用保守的配色策略，确保在所有环境下可读
- 简化颜色逻辑，降低出错概率

### 2.2 技术实现
```typescript
// 安全配色方案
private getSafeColors() {
  return {
    heading1: {
      background: 'var(--background-secondary)',
      text: 'var(--text-normal)',
      border: 'var(--text-accent)'
    },
    heading2: {
      background: 'var(--background-modifier-hover)',
      text: 'var(--text-normal)',
      border: 'var(--text-muted)'
    }
    // 统一使用Obsidian变量
  };
}
```

### 2.3 优缺点分析
**优点**：
- 实现简单快速
- 100%兼容性保证
- 维护成本低
- 立即可用

**缺点**：
- 视觉效果相对单调
- 缺乏层次感
- 可能影响用户体验

### 2.4 工作量评估
- 开发时间：1小时
- 测试时间：30分钟
- 总计：1.5小时

## 方案三：用户自定义配色方案

### 3.1 方案描述
- 在设置页面添加颜色自定义选项
- 提供多套预设配色方案
- 允许用户完全自定义节点颜色
- 实时预览效果

### 3.2 技术实现
```typescript
// 设置接口
interface ColorSettings {
  preset: 'auto' | 'safe' | 'custom';
  customColors: {
    heading1: { bg: string; text: string; border: string };
    heading2: { bg: string; text: string; border: string };
    // ...
  };
}

// 颜色应用逻辑
private applyUserColors(settings: ColorSettings) {
  switch (settings.preset) {
    case 'auto': return this.getThemeAwareColors();
    case 'safe': return this.getSafeColors();
    case 'custom': return settings.customColors;
  }
}
```

### 3.3 优缺点分析
**优点**：
- 最大灵活性
- 满足不同用户需求
- 可以解决所有颜色问题
- 用户体验最佳

**缺点**：
- 开发工作量大
- 增加设置复杂度
- 需要额外的UI设计
- 可能引入新的bug

### 3.4 工作量评估
- 开发时间：6-8小时
- UI设计：2小时
- 测试时间：2小时
- 总计：10-12小时

## 推荐方案

**建议采用方案一（智能主题适配方案）**，理由如下：

1. **最佳性价比**：工作量适中，效果显著
2. **用户体验优秀**：自动适配，无需用户干预
3. **技术先进性**：体现了对Obsidian生态的深度理解
4. **可扩展性**：为后续功能扩展奠定基础

## 实施建议

1. **第一阶段**：实施方案一，解决核心问题
2. **第二阶段**：如果用户反馈需要更多自定义，可以考虑添加方案三的部分功能
3. **备用方案**：如果方案一遇到技术困难，可以快速回退到方案二

## 验收标准

修复完成后应满足：
1. 在明亮主题下文字清晰可读
2. 在暗黑主题下文字清晰可读
3. 主题切换时自动更新颜色
4. 对比度符合可访问性标准
5. 不影响现有功能
