# 文档文字显示问题 - 计划

## 实施方案：智能主题适配方案

基于用户确认，将实施智能主题适配方案来解决文档文字显示问题。

## 1.1 主题检测功能实现

### 文件路径
- `src/core/VisJsRenderer.ts`

### 类/方法名
- 新增方法：`getThemeAwareColors()`
- 新增方法：`detectCurrentTheme()`
- 新增方法：`setupThemeListener()`

### 修改范围
- 第150-193行：替换硬编码的groups配置
- 新增主题检测逻辑（约30行代码）

### 预期结果
- 能够动态检测Obsidian当前主题（明亮/暗黑）
- 返回适配当前主题的颜色配置对象

## 1.2 动态颜色配置系统

### 文件路径
- `src/core/VisJsRenderer.ts`

### 类/方法名
- 修改方法：`getVisOptions()`
- 新增方法：`getColorSchemeForTheme(isDark: boolean)`

### 修改范围
- 第80-100行：修改getVisOptions方法调用
- 新增颜色方案配置（约50行代码）

### 预期结果
- 明亮主题：使用浅色背景+深色文字的高对比度配色
- 暗黑主题：使用深色背景+浅色文字的高对比度配色
- 所有节点类型都有对应的主题适配颜色

## 1.3 主题变化监听机制

### 文件路径
- `src/core/VisJsRenderer.ts`

### 类/方法名
- 修改方法：`constructor()`
- 新增方法：`onThemeChange()`
- 修改方法：`destroy()`

### 修改范围
- 构造函数：添加主题监听器初始化
- 新增主题变化处理逻辑（约20行代码）
- 销毁方法：清理监听器

### 预期结果
- 当用户切换Obsidian主题时，思维导图自动更新颜色
- 无需刷新页面即可看到颜色变化
- 正确清理事件监听器，避免内存泄漏

## 1.4 颜色对比度优化

### 文件路径
- `src/core/VisJsRenderer.ts`

### 类/方法名
- 新增方法：`ensureContrastRatio()`
- 新增方法：`calculateLuminance()`

### 修改范围
- 新增颜色对比度计算工具函数（约40行代码）
- 在颜色配置中应用对比度检查

### 预期结果
- 所有文字与背景的对比度达到WCAG AA标准（4.5:1）
- 自动调整颜色亮度以确保可读性
- 在极端主题下仍能保持良好的视觉效果

## 1.5 备用渲染器同步更新

### 文件路径
- `src/core/MindMapRenderer.ts`

### 类/方法名
- 修改方法：`getNodeColor()`
- 新增方法：`getThemeAwareNodeStyle()`

### 修改范围
- 第242-274行：更新getEnhancedNodeStyle方法
- 添加主题检测逻辑（约30行代码）

### 预期结果
- 确保备用渲染器也支持主题适配
- 保持两个渲染器的颜色一致性
- 为未来的渲染器切换做好准备

## 1.6 CSS样式文件更新

### 文件路径
- `styles.css`

### 类/方法名
- 更新CSS变量定义
- 添加主题相关的样式类

### 修改范围
- 第1-50行：添加新的CSS变量定义
- 新增主题适配样式类（约20行代码）

### 预期结果
- 提供更多可用的颜色变量
- 支持CSS层面的主题切换
- 与JavaScript逻辑保持一致

## 1.7 错误处理和回退机制

### 文件路径
- `src/core/VisJsRenderer.ts`

### 类/方法名
- 修改所有新增方法：添加try-catch块
- 新增方法：`getFallbackColors()`

### 修改范围
- 在所有主题检测相关方法中添加错误处理
- 新增安全回退颜色配置（约20行代码）

### 预期结果
- 当主题检测失败时，使用安全的默认颜色
- 记录错误日志便于调试
- 确保插件在任何情况下都能正常工作

## 实施顺序

1. **步骤1.1** → **步骤1.2**：先实现基础的主题检测和颜色配置
2. **步骤1.3**：添加动态监听机制
3. **步骤1.4**：优化颜色对比度
4. **步骤1.5** → **步骤1.6**：同步更新其他组件
5. **步骤1.7**：完善错误处理

## 验收标准

每个步骤完成后需要验证：
- 代码编译无错误
- 在明亮主题下文字清晰可读
- 在暗黑主题下文字清晰可读
- 主题切换时颜色正确更新
- 不影响现有的思维导图功能

## 风险控制

- 每次修改后立即测试，确保不破坏现有功能
- 保留原始颜色配置作为备份
- 如遇到问题，可以快速回退到安全配色方案
